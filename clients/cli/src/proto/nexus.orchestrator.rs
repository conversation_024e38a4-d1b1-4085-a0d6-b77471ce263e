// This file is @generated by prost-build.
/// Register a User.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RegisterUserRequest {
    /// UUIDv4 identifier for the user.
    #[prost(string, tag = "1")]
    pub uuid: ::prost::alloc::string::String,
    /// The user's wallet public address.
    #[prost(string, tag = "2")]
    pub wallet_address: ::prost::alloc::string::String,
}
/// Register a node.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RegisterNodeRequest {
    /// The type of this node.
    #[prost(enumeration = "NodeType", tag = "1")]
    pub node_type: i32,
    /// The owner of the node.
    #[prost(string, tag = "2")]
    pub user_id: ::prost::alloc::string::String,
}
/// Response to a node registration request.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RegisterNodeResponse {
    /// The node's ID.
    #[prost(string, tag = "1")]
    pub node_id: ::prost::alloc::string::String,
}
/// A Prover task
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Task {
    #[prost(string, tag = "1")]
    pub task_id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub program_id: ::prost::alloc::string::String,
    #[deprecated]
    #[prost(bytes = "vec", tag = "3")]
    pub public_inputs: ::prost::alloc::vec::Vec<u8>,
    #[prost(message, optional, tag = "4")]
    pub created_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(bytes = "vec", repeated, tag = "5")]
    pub public_inputs_list: ::prost::alloc::vec::Vec<::prost::alloc::vec::Vec<u8>>,
    /// The type of task (proof required or only hash)
    #[prost(enumeration = "TaskType", tag = "6")]
    pub task_type: i32,
}
/// Get outstanding tasks for a node.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetTasksRequest {
    #[prost(string, tag = "1")]
    pub node_id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub next_cursor: ::prost::alloc::string::String,
}
/// Tasks assigned to a node.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetTasksResponse {
    #[prost(message, repeated, tag = "1")]
    pub tasks: ::prost::alloc::vec::Vec<Task>,
    #[prost(string, tag = "2")]
    pub next_cursor: ::prost::alloc::string::String,
}
/// Request a prover task.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetProofTaskRequest {
    /// This node's ID.
    #[prost(string, tag = "1")]
    pub node_id: ::prost::alloc::string::String,
    /// The type of this node.
    #[prost(enumeration = "NodeType", tag = "2")]
    pub node_type: i32,
    /// The client's Ed25519 public key for proof authentication.Add commentMore actions
    #[prost(bytes = "vec", tag = "3")]
    pub ed25519_public_key: ::prost::alloc::vec::Vec<u8>,
    /// The maximum difficulty level the client wants to handle
    #[prost(enumeration = "TaskDifficulty", tag = "4")]
    pub max_difficulty: i32,
}
/// A Prover task.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetProofTaskResponse {
    /// Program id. (Assuming client-side default programs)
    #[prost(string, tag = "1")]
    pub program_id: ::prost::alloc::string::String,
    /// Public inputs to the program.
    #[prost(bytes = "vec", tag = "2")]
    pub public_inputs: ::prost::alloc::vec::Vec<u8>,
    /// The task's ID.
    #[prost(string, tag = "3")]
    pub task_id: ::prost::alloc::string::String,
}
/// Submit the result of a prover task.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubmitProofRequest {
    /// The type of this node.
    #[prost(enumeration = "NodeType", tag = "2")]
    pub node_type: i32,
    /// Hash of the proof.
    #[prost(string, tag = "3")]
    pub proof_hash: ::prost::alloc::string::String,
    /// Telemetry data about the node
    #[prost(message, optional, tag = "4")]
    pub node_telemetry: ::core::option::Option<NodeTelemetry>,
    /// ZK proof of the proof activity
    #[prost(bytes = "vec", tag = "5")]
    pub proof: ::prost::alloc::vec::Vec<u8>,
    /// The task's ID.
    #[prost(string, tag = "6")]
    pub task_id: ::prost::alloc::string::String,
    /// A Ed25519 public key (~32 bytes) generated by
    /// the node to uniquely identify this request,
    /// used for DoS protection.
    #[prost(bytes = "vec", tag = "7")]
    pub ed25519_public_key: ::prost::alloc::vec::Vec<u8>,
    /// A signature of the proof, generated by signing
    /// task_id + hash(proof) with the Ed25519 private key
    /// corresponding to the public key.
    #[prost(bytes = "vec", tag = "8")]
    pub signature: ::prost::alloc::vec::Vec<u8>,
}
/// Performance stats of a node.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct NodeTelemetry {
    /// Flops per second
    #[prost(int32, optional, tag = "1")]
    pub flops_per_sec: ::core::option::Option<i32>,
    /// Memory used in bytes for the proof activity
    #[prost(int32, optional, tag = "2")]
    pub memory_used: ::core::option::Option<i32>,
    /// Memory capacity in bytes of the node
    #[prost(int32, optional, tag = "3")]
    pub memory_capacity: ::core::option::Option<i32>,
    /// Geo location of the node
    #[prost(string, optional, tag = "4")]
    pub location: ::core::option::Option<::prost::alloc::string::String>,
}
/// Node information
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Node {
    /// The node's ID
    #[prost(string, tag = "1")]
    pub node_id: ::prost::alloc::string::String,
    /// The type of node
    #[prost(enumeration = "NodeType", tag = "2")]
    pub node_type: i32,
}
/// Response to get a single node by ID
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetNodeResponse {
    /// The wallet address of the node's owner
    #[prost(string, tag = "1")]
    pub wallet_address: ::prost::alloc::string::String,
}
/// Response returning all nodes associated with a user
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UserResponse {
    /// The user's registered nodes
    #[prost(message, repeated, tag = "1")]
    pub nodes: ::prost::alloc::vec::Vec<Node>,
    /// Cursor to fetch the next page of nodes, empty if there are no more results
    #[prost(string, tag = "2")]
    pub nodes_next_cursor: ::prost::alloc::string::String,
    /// The user's ID.
    #[prost(string, tag = "3")]
    pub user_id: ::prost::alloc::string::String,
    /// The user's wallet address
    #[prost(string, tag = "4")]
    pub wallet_address: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum NodeType {
    /// The node is a web prover.
    WebProver = 0,
    /// The node is a CLI prover.
    CliProver = 1,
}
impl NodeType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::WebProver => "WEB_PROVER",
            Self::CliProver => "CLI_PROVER",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "WEB_PROVER" => Some(Self::WebProver),
            "CLI_PROVER" => Some(Self::CliProver),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum TaskDifficulty {
    /// Small difficulty bucket
    Small = 0,
    /// Medium difficulty bucket
    Medium = 5,
    /// Large difficulty bucket
    Large = 10,
}
impl TaskDifficulty {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::Small => "SMALL",
            Self::Medium => "MEDIUM",
            Self::Large => "LARGE",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "SMALL" => Some(Self::Small),
            "MEDIUM" => Some(Self::Medium),
            "LARGE" => Some(Self::Large),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum TaskType {
    /// Task requires a proof to be submitted
    ProofRequired = 0,
    /// Task does not require a proof to be submitted
    ProofHash = 1,
}
impl TaskType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::ProofRequired => "PROOF_REQUIRED",
            Self::ProofHash => "PROOF_HASH",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "PROOF_REQUIRED" => Some(Self::ProofRequired),
            "PROOF_HASH" => Some(Self::ProofHash),
            _ => None,
        }
    }
}
