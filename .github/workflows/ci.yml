name: ci

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - "**"
  workflow_dispatch:  # Allow manual triggering

jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            clients/cli
            proto

      # Install Rust toolchain used by CLI
      # When passing an explicit toolchain... use "dtolnay/rust-toolchain@master""
      - name: Install Rust
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: nightly-2025-04-06
          target: x86_64-unknown-linux-gnu
          components: rustfmt, clippy

      - name: Set up Rust cache
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: ./clients/cli

      - name: Install Protobuf Compiler
        run: |
          sudo apt-get update
          sudo apt-get install -y protobuf-compiler

      - name: Check formatting
        working-directory: clients/cli
        run: cargo fmt --all -- --check

      - name: Run cargo clippy
        working-directory: clients/cli
        run: cargo clippy --profile=ci-build --no-deps --package nexus-network -- -D warnings

      - name: Build
        working-directory: clients/cli
        run: cargo build --profile=ci-build

      - name: Test
        working-directory: clients/cli
        run: cargo test --profile=ci-build --tests

      - name: CLI smoke test (help)
        working-directory: clients/cli
        run: cargo run --profile=ci-build -- --help

      - name: Ensure checked in generated files are up to date
        run: |
          if [ -n "$(git status --porcelain)" ]; then \
              echo "There are uncommitted changes in working tree after building."; \
              git status; \
              git --no-pager diff; \
              exit 1; \
          else \
              echo "Git working tree is clean"; \
          fi;
