// Copyright (c) 2025 Nexus. All rights reserved.
//
// If you use this protocol to communicate with Nexus's servers,
// you must agree to the Terms of Use: https://nexus.xyz/terms-of-use

syntax = "proto3";

package nexus.orchestrator;

import "google/protobuf/timestamp.proto";

// Register a User.
message RegisterUserRequest {
  // UUIDv4 identifier for the user.
  string uuid = 1;

  // The user's wallet public address.
  string wallet_address = 2;
}

enum NodeType {
  // The node is a web prover.
  WEB_PROVER = 0;

  // The node is a CLI prover.
  CLI_PROVER = 1;
}

// Register a node.
message RegisterNodeRequest {
  // The type of this node.
  NodeType node_type = 1;

  // The owner of the node.
  string user_id = 2;
}

// Response to a node registration request.
message RegisterNodeResponse {
  // The node's ID.
  string node_id = 1;
}

// A Prover task
message Task {
  string task_id = 1;
  string program_id = 2;
  bytes public_inputs = 3 [deprecated = true];
  google.protobuf.Timestamp created_at = 4;
  repeated bytes public_inputs_list = 5;
  // The type of task (proof required or only hash)
  TaskType task_type = 6;
}

// Get outstanding tasks for a node.
message GetTasksRequest {
  string node_id = 1;
  string next_cursor = 2;
}

// Tasks assigned to a node.
message GetTasksResponse {
  repeated Task tasks = 1;
  string next_cursor = 2;
}

// Request a prover task.
message GetProofTaskRequest {
  // This node's ID.
  string node_id = 1;

  // The type of this node.
  NodeType node_type = 2;

  // The client's Ed25519 public key for proof authentication.Add commentMore actions
  bytes ed25519_public_key = 3;

  // The maximum difficulty level the client wants to handle
  TaskDifficulty max_difficulty = 4;
}

// A Prover task.
message GetProofTaskResponse {
  // Program id. (Assuming client-side default programs)
  string program_id = 1;

  // Public inputs to the program.
  bytes public_inputs = 2;

  // The task's ID.
  string task_id = 3;
}

// Submit the result of a prover task.
message SubmitProofRequest {
  reserved 1;
  reserved "node_id";

  // The type of this node.
  NodeType node_type = 2;

  // Hash of the proof.
  string proof_hash = 3;

  // Telemetry data about the node
  NodeTelemetry node_telemetry = 4;

  // ZK proof of the proof activity
  bytes proof = 5;

  // The task's ID.
  string task_id = 6;

  // A Ed25519 public key (~32 bytes) generated by
  // the node to uniquely identify this request,
  // used for DoS protection.
  bytes ed25519_public_key = 7;

  // A signature of the proof, generated by signing
  // task_id + hash(proof) with the Ed25519 private key
  // corresponding to the public key.
  bytes signature = 8;
}

// Performance stats of a node.
message NodeTelemetry {
  // Flops per second
  optional int32 flops_per_sec = 1;

  // Memory used in bytes for the proof activity
  optional int32 memory_used = 2;

  // Memory capacity in bytes of the node
  optional int32 memory_capacity = 3;

  // Geo location of the node
  optional string location = 4;
}

// Node information
message Node {
  // The node's ID
  string node_id = 1;
  // The type of node
  NodeType node_type = 2;
}

enum TaskDifficulty {
  // Small difficulty bucket
  SMALL = 0;

  reserved 1 to 4;

  // Medium difficulty bucket
  MEDIUM = 5;
  
  reserved 6 to 9;

  // Large difficulty bucket
  LARGE = 10;
}

enum TaskType {
  // Task requires a proof to be submitted
  PROOF_REQUIRED = 0;
  
  // Task does not require a proof to be submitted
  PROOF_HASH = 1;
}

// Response to get a single node by ID
message GetNodeResponse {
  // The wallet address of the node's owner
  string wallet_address = 1;
}

// Response returning all nodes associated with a user
message UserResponse {
  // The user's registered nodes
  repeated Node nodes = 1;
  // Cursor to fetch the next page of nodes, empty if there are no more results
  string nodes_next_cursor = 2;
  // The user's ID.
  string user_id = 3;
  // The user's wallet address
  string wallet_address = 4;
}
